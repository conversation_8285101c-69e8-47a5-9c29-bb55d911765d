<template>
    <div>
        <!-- Error Messages -->
        <ion-note v-if="Object.values(validationMessages).some(msg => msg)" color="danger" class="error-note">
            Please review form for errors
        </ion-note>

        <!-- Followup Section -->
        <ion-card>
            <ion-card-header>
                <ion-card-title>{{ formModel.followup.label }}</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <div v-for="option in formModel.followup.options()" :key="option.id">
                    <ion-item>
                        <ion-label>{{ option.label }}</ion-label>
                        <ion-select :value="formData[option.id]"
                            @ion-change="(e: any) => { formData[option.id] = e.detail.value; dataHandler(option.id) }"
                            interface="popover" placeholder="Select option">
                            <ion-select-option v-for="opt in option.options" :key="opt" :value="opt">
                                {{ opt }}
                            </ion-select-option>
                        </ion-select>
                    </ion-item>
                    <ion-note v-if="validationMessages[option.id]" color="danger">
                        {{ validationMessages[option.id] }}
                    </ion-note>
                </div>
            </ion-card-content>
        </ion-card>

        <!-- Has Linkage Section -->
        <ion-card>
            <ion-card-header>
                <ion-card-title>{{ formModel.has_linkage.label }}</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <ion-item>
                    <ion-label>{{ formModel.has_linkage.label }}</ion-label>
                    <ion-select :value="formData.has_linkage"
                        @ion-change="(e: any) => { formData.has_linkage = e.detail.value; dataHandler('has_linkage') }"
                        interface="popover" placeholder="Select option">
                        <ion-select-option v-for="option in formModel.has_linkage.options()" :key="option"
                            :value="option">
                            {{ option }}
                        </ion-select-option>
                    </ion-select>
                </ion-item>
                <ion-note v-if="validationMessages.has_linkage" color="danger">
                    {{ validationMessages.has_linkage }}
                </ion-note>

                <!-- Linkage Number (conditional) -->
                <div v-if="formModel.has_linkage.linkage_number.required()">
                    <ion-item>
                        <ion-label position="stacked">{{ formModel.has_linkage.linkage_number.label }}</ion-label>
                        <ion-input :value="formData.linkage_number"
                            @ion-input="(e: any) => { formData.linkage_number = e.detail.value; dataHandler('linkage_number') }"
                            fill="outline" placeholder="Enter linkage number"></ion-input>
                    </ion-item>
                    <ion-note v-if="validationMessages.linkage_number" color="danger">
                        {{ validationMessages.linkage_number }}
                    </ion-note>
                </div>
            </ion-card-content>
        </ion-card>

        <!-- Received ARVs Section -->
        <ion-card>
            <ion-card-header>
                <ion-card-title>ARV History</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <ion-item>
                    <ion-label>{{ formModel.has_linkage.received_arvs.label }}</ion-label>
                    <ion-select :value="formData.received_arvs"
                        @ion-change="(e: any) => { formData.received_arvs = e.detail.value; dataHandler('received_arvs') }"
                        interface="popover" placeholder="Select option">
                        <ion-select-option v-for="option in formModel.has_linkage.received_arvs.options()" :key="option"
                            :value="option">
                            {{ option }}
                        </ion-select-option>
                    </ion-select>
                </ion-item>
                <ion-note v-if="validationMessages.received_arvs" color="danger">
                    {{ validationMessages.received_arvs }}
                </ion-note>

                <!-- Date Last Taken ARVs (conditional) -->
                <div v-if="formModel.has_linkage.date_last_taken_arvs.required()">
                    <ion-item>
                        <ion-label position="stacked">{{ formModel.has_linkage.date_last_taken_arvs.label }}</ion-label>
                        <ion-datetime :value="formData.date_last_taken_arvs"
                            @ion-change="(e: any) => { formData.date_last_taken_arvs = e.detail.value; dataHandler('date_last_taken_arvs') }"
                            display-format="YYYY-MM-DD" picker-format="YYYY-MM-DD"
                            placeholder="Select date"></ion-datetime>
                    </ion-item>
                    <ion-note v-if="validationMessages.date_last_taken_arvs" color="danger">
                        {{ validationMessages.date_last_taken_arvs }}
                    </ion-note>
                </div>

                <!-- Taken Last Two Months (conditional) -->
                <div v-if="formModel.has_linkage.taken_last_two_months.required()">
                    <ion-item>
                        <ion-label>{{ formModel.has_linkage.taken_last_two_months.label }}</ion-label>
                        <ion-select :value="formData.taken_last_two_months"
                            @ion-change="(e: any) => { formData.taken_last_two_months = e.detail.value; dataHandler('taken_last_two_months') }"
                            interface="popover" placeholder="Select option">
                            <ion-select-option v-for="option in formModel.has_linkage.taken_last_two_months.options()"
                                :key="option" :value="option">
                                {{ option }}
                            </ion-select-option>
                        </ion-select>
                    </ion-item>
                    <ion-note v-if="validationMessages.taken_last_two_months" color="danger">
                        {{ validationMessages.taken_last_two_months }}
                    </ion-note>
                </div>

                <!-- Taken Last Two Weeks (conditional) -->
                <div v-if="formModel.has_linkage.taken_last_two_weeks.required()">
                    <ion-item>
                        <ion-label>{{ formModel.has_linkage.taken_last_two_weeks.label }}</ion-label>
                        <ion-select :value="formData.taken_last_two_weeks"
                            @ion-change="(e: any) => { formData.taken_last_two_weeks = e.detail.value; dataHandler('taken_last_two_weeks') }"
                            interface="popover" placeholder="Select option">
                            <ion-select-option v-for="option in formModel.has_linkage.taken_last_two_weeks.options()"
                                :key="option" :value="option">
                                {{ option }}
                            </ion-select-option>
                        </ion-select>
                    </ion-item>
                    <ion-note v-if="validationMessages.taken_last_two_weeks" color="danger">
                        {{ validationMessages.taken_last_two_weeks }}
                    </ion-note>
                </div>
            </ion-card-content>
        </ion-card>

        <!-- ART Clinic Registration Section -->
        <ion-card>
            <ion-card-header>
                <ion-card-title>ART Clinic Registration</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <!-- Ever Registered at ART Clinic -->
                <div v-if="formModel.has_linkage.ever_registered_at_art_clinic.required()">
                    <ion-item>
                        <ion-label>{{ formModel.has_linkage.ever_registered_at_art_clinic.label }}</ion-label>
                        <ion-select :value="formData.ever_registered_at_art_clinic"
                            @ion-change="(e: any) => { formData.ever_registered_at_art_clinic = e.detail.value; dataHandler('ever_registered_at_art_clinic') }"
                            interface="popover" placeholder="Select option">
                            <ion-select-option
                                v-for="option in formModel.has_linkage.ever_registered_at_art_clinic.options()"
                                :key="option" :value="option">
                                {{ option }}
                            </ion-select-option>
                        </ion-select>
                    </ion-item>
                    <ion-note v-if="validationMessages.ever_registered_at_art_clinic" color="danger">
                        {{ validationMessages.ever_registered_at_art_clinic }}
                    </ion-note>
                </div>

                <!-- Location of ART Initialization (conditional) -->
                <div v-if="formModel.has_linkage.location_of_art_initialization.required()">
                    <ion-item>
                        <ion-label position="stacked">{{ formModel.has_linkage.location_of_art_initialization.label
                            }}</ion-label>
                        <ion-searchbar :value="formData.location_of_art_initialization"
                            @ion-input="(e: any) => { formData.location_of_art_initialization = e.detail.value; formModel.has_linkage.location_of_art_initialization.searchFacilities(e.detail.value); dataHandler('location_of_art_initialization') }"
                            placeholder="Search for facility" :debounce="300"></ion-searchbar>
                    </ion-item>
                    <ion-list v-if="facilities.length > 0">
                        <ion-item v-for="facility in facilities" :key="facility.value" button
                            @click="formData.location_of_art_initialization = facility.label; facilities = []; dataHandler('location_of_art_initialization')">
                            <ion-label>{{ facility.label }}</ion-label>
                        </ion-item>
                    </ion-list>
                    <ion-note v-if="validationMessages.location_of_art_initialization" color="danger">
                        {{ validationMessages.location_of_art_initialization }}
                    </ion-note>
                </div>
                <!-- ART Start Date (conditional) -->
                <div v-if="formModel.has_linkage.art_start_date.required()">
                    <ion-item>
                        <ion-label position="stacked">{{ formModel.has_linkage.art_start_date.label }}</ion-label>
                        <ion-datetime :value="formData.art_start_date"
                            @ion-change="(e: any) => { formData.art_start_date = e.detail.value; dataHandler('art_start_date') }"
                            display-format="YYYY-MM-DD" picker-format="YYYY-MM-DD"
                            placeholder="Select date"></ion-datetime>
                    </ion-item>
                    <ion-note v-if="validationMessages.art_start_date" color="danger">
                        {{ validationMessages.art_start_date }}
                    </ion-note>
                </div>

                <!-- ART Number at Previous Location (conditional) -->
                <div v-if="formModel.has_linkage.art_number_at_previous_location.required()">
                    <ion-item>
                        <ion-label position="stacked">{{ formModel.has_linkage.art_number_at_previous_location.label
                        }}</ion-label>
                        <ion-input :value="formData.art_number_at_previous_location"
                            @ion-input="(e: any) => { formData.art_number_at_previous_location = e.detail.value; dataHandler('art_number_at_previous_location') }"
                            fill="outline" placeholder="Enter ART number"></ion-input>
                    </ion-item>
                    <ion-note v-if="validationMessages.art_number_at_previous_location" color="danger">
                        {{ validationMessages.art_number_at_previous_location }}
                    </ion-note>
                </div>

                <!-- Has Transfer Letter (conditional) -->
                <div v-if="formModel.has_linkage.has_transfer_letter.required()">
                    <ion-item>
                        <ion-label>{{ formModel.has_linkage.has_transfer_letter.label }}</ion-label>
                        <ion-select :value="formData.has_transfer_letter"
                            @ion-change="(e: any) => { formData.has_transfer_letter = e.detail.value; dataHandler('has_transfer_letter') }"
                            interface="popover" placeholder="Select option">
                            <ion-select-option v-for="option in formModel.has_linkage.has_transfer_letter.options()"
                                :key="option" :value="option">
                                {{ option }}
                            </ion-select-option>
                        </ion-select>
                    </ion-item>
                    <ion-note v-if="validationMessages.has_transfer_letter" color="danger">
                        {{ validationMessages.has_transfer_letter }}
                    </ion-note>
                </div>
            </ion-card-content>
        </ion-card>

        <!-- Initial Measurements Section -->
        <ion-card>
            <ion-card-header>
                <ion-card-title>Initial Measurements</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <!-- Initial Height (conditional) -->
                <div v-if="formModel.has_linkage.initial_height.required()">
                    <ion-item>
                        <ion-label position="stacked">{{ formModel.has_linkage.initial_height.label }} (cm)</ion-label>
                        <ion-input :value="formData.initial_height"
                            @ion-input="(e: any) => { formData.initial_height = e.detail.value; dataHandler('initial_height') }"
                            fill="outline" type="number" placeholder="Enter height in cm"></ion-input>
                    </ion-item>
                    <ion-note v-if="validationMessages.initial_height" color="danger">
                        {{ validationMessages.initial_height }}
                    </ion-note>
                </div>

                <!-- Initial Weight (conditional) -->
                <div v-if="formModel.has_linkage.initial_weight.required()">
                    <ion-item>
                        <ion-label position="stacked">{{ formModel.has_linkage.initial_weight.label }} (kg)</ion-label>
                        <ion-input :value="formData.initial_weight"
                            @ion-input="(e: any) => { formData.initial_weight = e.detail.value; dataHandler('initial_weight') }"
                            fill="outline" type="number" placeholder="Enter weight in kg"></ion-input>
                    </ion-item>
                    <ion-note v-if="validationMessages.initial_weight" color="danger">
                        {{ validationMessages.initial_weight }}
                    </ion-note>
                </div>

                <!-- CD4 Available (conditional) -->
                <div v-if="formModel.has_linkage.cd4_available.required()">
                    <ion-item>
                        <ion-label>{{ formModel.has_linkage.cd4_available.label }}</ion-label>
                        <ion-select :value="formData.cd4_available"
                            @ion-change="(e: any) => { formData.cd4_available = e.detail.value; dataHandler('cd4_available') }"
                            interface="popover" placeholder="Select option">
                            <ion-select-option v-for="option in formModel.has_linkage.cd4_available.options()"
                                :key="option" :value="option">
                                {{ option }}
                            </ion-select-option>
                        </ion-select>
                    </ion-item>
                    <ion-note v-if="validationMessages.cd4_available" color="danger">
                        {{ validationMessages.cd4_available }}
                    </ion-note>
                </div>

                <!-- CD4 Percent (conditional) -->
                <div v-if="formModel.has_linkage.cd4_percent.required()">
                    <ion-item>
                        <ion-label position="stacked">{{ formModel.has_linkage.cd4_percent.label }} (%)</ion-label>
                        <ion-input :value="formData.cd4_percent"
                            @ion-input="(e: any) => { formData.cd4_percent = e.detail.value; dataHandler('cd4_percent') }"
                            fill="outline" type="number" placeholder="Enter CD4 percentage"></ion-input>
                    </ion-item>
                    <ion-note v-if="validationMessages.cd4_percent" color="danger">
                        {{ validationMessages.cd4_percent }}
                    </ion-note>
                </div>
            </ion-card-content>
        </ion-card>

        <!-- HIV Test Information Section -->
        <ion-card>
            <ion-card-header>
                <ion-card-title>HIV Test Information</ion-card-title>
            </ion-card-header>
            <ion-card-content>
                <!-- Confirmatory HIV Test Type -->
                <ion-item>
                    <ion-label>{{ formModel.has_linkage.confirmatory_hiv_test_type.label }}</ion-label>
                    <ion-select :value="formData.confirmatory_hiv_test_type"
                        @ion-change="(e: any) => { formData.confirmatory_hiv_test_type = e.detail.value; dataHandler('confirmatory_hiv_test_type') }"
                        interface="popover" placeholder="Select test type">
                        <ion-select-option v-for="option in formModel.has_linkage.confirmatory_hiv_test_type.options()"
                            :key="option.value" :value="option.value" :disabled="option.disabled">
                            {{ option.label }}
                        </ion-select-option>
                    </ion-select>
                </ion-item>
                <ion-note v-if="validationMessages.confirmatory_hiv_test_type" color="danger">
                    {{ validationMessages.confirmatory_hiv_test_type }}
                </ion-note>

                <!-- Confirmatory HIV Test Location (conditional) -->
                <div v-if="formModel.has_linkage.confirmatory_hiv_test_location.required()">
                    <ion-item>
                        <ion-label position="stacked">{{ formModel.has_linkage.confirmatory_hiv_test_location.label
                        }}</ion-label>
                        <ion-searchbar :value="formData.confirmatory_hiv_test_location"
                            @ion-input="(e: any) => { formData.confirmatory_hiv_test_location = e.detail.value; formModel.has_linkage.confirmatory_hiv_test_location.searchFacilities(e.detail.value); dataHandler('confirmatory_hiv_test_location') }"
                            placeholder="Search for facility" :debounce="300"></ion-searchbar>
                    </ion-item>
                    <ion-list v-if="facilities.length > 0">
                        <ion-item v-for="facility in facilities" :key="facility.value" button
                            @click="formData.confirmatory_hiv_test_location = facility.label; facilities = []; dataHandler('confirmatory_hiv_test_location')">
                            <ion-label>{{ facility.label }}</ion-label>
                        </ion-item>
                    </ion-list>
                    <ion-note v-if="validationMessages.confirmatory_hiv_test_location" color="danger">
                        {{ validationMessages.confirmatory_hiv_test_location }}
                    </ion-note>
                </div>

                <!-- Confirmatory HIV Test Date (conditional) -->
                <div v-if="formModel.has_linkage.confirmatory_hiv_test_date.required()">
                    <ion-item>
                        <ion-label position="stacked">{{ formModel.has_linkage.confirmatory_hiv_test_date.label
                        }}</ion-label>
                        <ion-datetime :value="formData.confirmatory_hiv_test_date"
                            @ion-change="(e: any) => { formData.confirmatory_hiv_test_date = e.detail.value; dataHandler('confirmatory_hiv_test_date') }"
                            display-format="YYYY-MM-DD" picker-format="YYYY-MM-DD"
                            placeholder="Select date"></ion-datetime>
                    </ion-item>
                    <ion-note v-if="validationMessages.confirmatory_hiv_test_date" color="danger">
                        {{ validationMessages.confirmatory_hiv_test_date }}
                    </ion-note>
                </div>
            </ion-card-content>
        </ion-card>

        <!-- Submit Button -->
        <ion-card>
            <ion-card-content>
                <ion-button expand="block" @click="onSubmit" :disabled="isLoading">
                    <ion-spinner v-if="isLoading" name="dots"></ion-spinner>
                    <span v-else>Save ART Clinic Registration</span>
                </ion-button>
            </ion-card-content>
        </ion-card>
    </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import {
    IonContent,
    IonCard,
    IonCardHeader,
    IonCardTitle,
    IonCardContent,
    IonItem,
    IonLabel,
    IonSelect,
    IonSelectOption,
    IonInput,
    IonDatetime,
    IonSearchbar,
    IonList,
    IonNote,
    IonButton,
    IonSpinner
} from "@ionic/vue";
import { ARTClinicRegistrationService } from "../services/art_clinic_registration_service";
import { PatientService } from "@/services/patient_service";
import { validateScanFormLinkageCode } from "@/utils/Damm"
import { getFacilities } from "@/utils/HisFormHelpers/LocationFieldOptions";
import { toastDanger, toastSuccess, toastWarning } from "@/utils/Alerts";

const patient = new PatientService()
const service = new ARTClinicRegistrationService(patient.getID(), -1);

const formData = ref<any>({
    phone_followup: '' as string,
    home_visit_followup: '' as string,
    has_linkage: '' as string,
    linkage_number: '' as string,
    received_arvs: '' as string,
    date_last_taken_arvs: '' as string,
    taken_last_two_months: '' as string,
    taken_last_two_weeks: '' as string,
    ever_registered_at_art_clinic: '' as string,
    location_of_art_initialization: '' as string,
    art_number_at_previous_location: '' as string,
    confirmatory_hiv_test_type: '' as string,
    confirmatory_hiv_test_location: '' as string,
    confirmatory_hiv_test_date: '' as string,
    has_transfer_letter: '' as string,
    art_registration_date: '' as string,
    art_start_date: '' as string,
    initial_weight: '' as string,
    initial_height: '' as string,
    cd4_available: '' as string,
    cd4_percent: '' as string
})

const facilities = ref<any>([])
const validationMessages = ref<any>({})
const isLoading = ref(false)

const formModel: any = {
    followup: {
        required: () => true,
        label: 'Agrees to followup',
        buildObs: () => {
            return formModel.followup.options().map((o: any) => {
                return [
                    service.buildValueCoded(o.label, formData.value[o.id]),
                    service.buildValueCoded('Agrees to followup', o.label)
                ]
            }).flat(1)
        },
        options: () => {
            return [
                {
                    id: 'phone_followup',
                    label: 'Phone',
                    options: ['Yes', "No"]
                },
                {
                    id: 'home_visit_followup',
                    label: 'Home visit',
                    options: ['Yes', "No"]
                }
            ]
        },
        validation: () => {
            validationMessages.value['phone_followup'] = ''
            validationMessages.value['home_visit_followup'] = ''
            if (!formData.value.phone_followup) {
                validationMessages.value['phone_followup'] = 'Phone followup is required'
                return
            }
            if (!formData.value.home_visit_followup) {
                validationMessages.value['home_visit_followup'] = 'Home visit followup is required'
            }
        }
    },
    has_linkage: {
        required: () => true,
        label: 'Has linkage number',
        options: () => {
            return ['Yes', 'No']
        },
        validation: () => {
            validationMessages.value['has_linkage'] = ''
            if (!formData.value.has_linkage) {
                validationMessages.value['has_linkage'] = 'Has linkage is required'
            }
        },
        linkage_number: {
            required: () => formData.value.has_linkage === 'Yes',
            label: 'Linkage number',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'has_linkage' && value === 'Yes') {
                    formData.value.linkage_number = ''
                    validationMessages.value['linkage_number'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueText('HTC Serial number', formData.value.linkage_number)
            },
            validation: () => {
                validationMessages.value['linkage_number'] = ''
                const required = formModel.linkage_number.required()
                if (required && !formData.value.linkage_number) {
                    validationMessages.value['linkage_number'] = 'Linkage number is required'
                    return
                }
                if (required && !validateScanFormLinkageCode(formData.value.linkage_number)) {
                    validationMessages.value['linkage_number'] = 'Invalid linkage number'
                }
            }
        },
        received_arvs: {
            required: () => true,
            label: 'Received ARVs',
            options: () => {
                return ['Yes', 'No']
            },
            buildObs: () => {
                return service.buildValueCoded('Ever received ART', formData.value.received_arvs)
            },
            validation: () => {
                validationMessages.value['received_arvs'] = ''
                if (!formData.value.received_arvs) {
                    validationMessages.value['received_arvs'] = 'Received ARVs is required'
                }
            }
        },
        date_last_taken_arvs: {
            required: () => formData.value.received_arvs === 'Yes',
            label: 'Date last taken ARVs',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'received_arvs' && value === 'No') {
                    formData.value.date_last_taken_arvs = ''
                    validationMessages.value['date_last_taken_arvs'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueDate('Date ART last taken', formData.value.date_last_taken_arvs)
            },
            validation: () => {
                validationMessages.value['date_last_taken_arvs'] = ''
                const required = formModel.date_last_taken_arvs.required()
                if (required && !formData.value.date_last_taken_arvs) {
                    validationMessages.value['date_last_taken_arvs'] = 'Date last taken ARVs is required'
                }
            }
        },
        taken_last_two_months: {
            required: () => formData.value.received_arvs === 'Unknown',
            label: 'Taken last two months',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'received_arvs' && value == 'No') {
                    formData.value.taken_last_two_months = ''
                    validationMessages.value['taken_last_two_months'] = ''
                }
            },
            options: () => {
                return ['Yes', 'No', 'Unknown']
            },
            buildObs: () => {
                return service.buildValueCoded('Has the patient taken ART in the last two months', formData.value.taken_last_two_months)
            },
            validation: () => {
                validationMessages.value['taken_last_two_months'] = ''
                const required = formModel.taken_last_two_months.required()
                if (required && !formData.value.taken_last_two_months) {
                    validationMessages.value['taken_last_two_months'] = 'Taken last two months is required'
                }
            }
        },
        taken_last_two_weeks: {
            required: () => formData.value.taken_last_two_months === 'Yes',
            label: 'Taken last two weeks',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'taken_last_two_months' && value === 'No') {
                    formData.value.taken_last_two_weeks = ''
                    validationMessages.value['taken_last_two_weeks'] = ''
                }
            },
            options: () => {
                return ['Yes', 'No', 'Unknown']
            },
            buildObs: () => {
                return service.buildValueCoded('Has the patient taken ART in the last two weeks', formData.value.taken_last_two_weeks)
            },
            validation: () => {
                validationMessages.value['taken_last_two_weeks'] = ''
                const required = formModel.taken_last_two_weeks.required()
                if (required && !formData.value.taken_last_two_weeks) {
                    validationMessages.value['taken_last_two_weeks'] = 'Taken last two weeks is required'
                }
            }
        },
        ever_registered_at_art_clinic: {
            required: () => formData.value.received_arvs === 'Yes',
            label: 'Ever registered at ART clinic',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'received_arvs' && value === 'No') {
                    formData.value.ever_registered_at_art_clinic = ''
                    validationMessages.value['ever_registered_at_art_clinic'] = ''
                }
            },
            options: () => {
                return ['Yes', 'No']
            },
            buildObs: () => {
                return service.buildValueCoded('Ever registered at ART clinic', formData.value.ever_registered_at_art_clinic)
            },
            validation: () => {
                validationMessages.value['ever_registered_at_art_clinic'] = ''
                const required = formModel.ever_registered_at_art_clinic.required()
                if (required && !formData.value.ever_registered_at_art_clinic) {
                    validationMessages.value['ever_registered_at_art_clinic'] = 'Ever registered at ART clinic is required'
                }
            }
        },
        location_of_art_initialization: {
            required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
            label: 'Location of ART initialization',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'ever_registered_at_art_clinic' && value === 'No') {
                    formData.value.location_of_art_initialization = ''
                    validationMessages.value['location_of_art_initialization'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueText('Location of ART initialization', formData.value.location_of_art_initialization)
            },
            validation: () => {
                validationMessages.value['location_of_art_initialization'] = ''
                if (formData.value.ever_registered_at_art_clinic === 'Yes' && !formData.value.location_of_art_initialization) {
                    validationMessages.value['location_of_art_initialization'] = 'Location of ART initialization is required'
                }
            },
            searchFacilities: (q: any) => getFacilities(q).then((res) => facilities.value = res)
        },
        art_start_date: {
            required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
            label: 'ART start date',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'ever_registered_at_art_clinic' && value === 'No') {
                    formData.value.art_start_date = ''
                    validationMessages.value['art_start_date'] = ''
                }
            },
            buildObs: () => {
                return buildDateObs('Date ART started', formData.value.art_start_date, false)
            },
            validation: () => {
                validationMessages.value['art_start_date'] = ''
                const required = formModel.art_start_date.required()
                if (required && !formData.value.art_start_date) {
                    validationMessages.value['art_start_date'] = 'ART start date is required'
                    return
                }
                if (required && new Date(formData.value.art_start_date) > new Date()) {
                    validationMessages.value['art_start_date'] = 'ART start date cannot be in the future'
                }
                if (required && new Date(formData.value.art_start_date) < new Date(patient.getBirthdate())) {
                    validationMessages.value['art_start_date'] = 'ART start date cannot be before birthdate'
                }
            }
        },
        art_number_at_previous_location: {
            required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
            label: 'ART number at previous location',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'ever_registered_at_art_clinic' && value === 'No') {
                    formData.value.art_number_at_previous_location = ''
                    validationMessages.value['art_number_at_previous_location'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueText('ART number at previous location', formData.value.art_number_at_previous_location)
            },
            validation: () => {
                validationMessages.value['art_number_at_previous_location'] = ''
                const required = formModel.art_number_at_previous_location.required()
                if (required && !formData.value.art_number_at_previous_location) {
                    validationMessages.value['art_number_at_previous_location'] = 'ART number at previous location is required'
                }
            }
        },
        has_transfer_letter: {
            required: () => formData.value.ever_registered_at_art_clinic === 'Yes',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'ever_registered_at_art_clinic' && value === 'No') {
                    formData.value.has_transfer_letter = ''
                    validationMessages.value['has_transfer_letter'] = ''
                }
            },
            label: 'Has stage information?',
            options: () => {
                return ['Yes', 'No']
            },
            buildObs: () => {
                return service.buildValueCoded('Has transfer letter', formData.value.has_transfer_letter)
            },
            validation: () => {
                validationMessages.value['has_transfer_letter'] = ''
                const required = formModel.has_transfer_letter.required()
                if (required && !formData.value.has_transfer_letter) {
                    validationMessages.value['has_transfer_letter'] = 'Has transfer letter is required'
                }
            }
        },
        initial_height: {
            required: () => formData.value.has_transfer_letter === 'Yes',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'has_transfer_letter' && value === 'No') {
                    formData.value.initial_height = ''
                    validationMessages.value['initial_height'] = ''
                }
            },
            label: 'Initial height',
            buildObs: () => {
                return service.buildValueNumber('Height', formData.value.initial_height)
            },
            validation: () => {
                validationMessages.value['initial_height'] = ''
                const required = formModel.initial_height.required()
                if (required && !formData.value.initial_height) {
                    validationMessages.value['initial_height'] = 'Initial height is required'
                    return
                }
                if (required && !Number.isInteger(formData.value.initial_height)) {
                    validationMessages.value['initial_height'] = 'Initial height must be a number'
                }
                if (required && formData.value.initial_height < 40) {
                    validationMessages.value['initial_height'] = 'Initial height cannot be less than 40'
                }
                if (required && formData.value.initial_height > 220) {
                    validationMessages.value['initial_height'] = 'Initial height cannot be greater than 220'
                }
            }
        },
        initial_weight: {
            required: () => formData.value.has_transfer_letter === 'Yes',
            label: 'Initial weight',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'has_transfer_letter' && value === 'No') {
                    formData.value.initial_weight = ''
                    validationMessages.value['initial_weight'] = ''
                }
            },
            buildObs: () => {
                return service.buildValueNumber('Weight', formData.value.initial_weight)
            },
            validation: () => {
                validationMessages.value['initial_weight'] = ''
                const required = formModel.initial_weight.required()
                if (required && !formData.value.initial_weight) {
                    validationMessages.value['initial_weight'] = 'Initial weight is required'
                    return
                }
                if (required && !Number.isInteger(formData.value.initial_weight)) {
                    validationMessages.value['initial_weight'] = 'Initial weight must be a number'
                }
                if (required && formData.value.initial_weight < 0.5) {
                    validationMessages.value['initial_weight'] = 'Initial weight cannot be less than 0.5'
                }
                if (required && formData.value.initial_weight > 250) {
                    validationMessages.value['initial_weight'] = 'Initial weight cannot be greater than 250'
                }
            }
        },
        cd4_available: {
            required: () => formData.value.has_transfer_letter === 'Yes',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'has_transfer_letter' && value === 'No') {
                    formData.value.cd4_available = ''
                    validationMessages.value['cd4_available'] = ''
                }
            },
            label: 'CD4 available',
            options: () => {
                return ['Yes', 'No']
            },
            validation: () => {
                validationMessages.value['cd4_available'] = ''
                if (!formData.value.cd4_available) {
                    validationMessages.value['cd4_available'] = 'CD4 available is required'
                }
            }
        },
        cd4_percent: {
            required: () => formData.value.cd4_available === 'Yes',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'cd4_available' && value === 'No') {
                    formData.value.cd4_percent = ''
                    validationMessages.value['cd4_percent'] = ''
                }
            },
            label: 'CD4 percent',
            buildObs: () => {
                return service.buildValueNumber(
                    'CD4 percent', parseInt(formData.value.cd4_percent.substring(1)), '%' as any
                )
            },
            validation: () => {
                validationMessages.value['cd4_percent'] = ''
                const required = formModel.cd4_percent.required()
                if (required && !formData.value.cd4_percent) {
                    validationMessages.value['cd4_percent'] = 'CD4 percent is required'
                    return
                }
                if (required && !Number.isInteger(formData.value.cd4_percent)) {
                    validationMessages.value['cd4_percent'] = 'CD4 percent must be a number'
                }
                if (required && formData.value.cd4_percent < 0) {
                    validationMessages.value['cd4_percent'] = 'CD4 percent cannot be less than 0'
                }
                if (required && formData.value.cd4_percent > 100) {
                    validationMessages.value['cd4_percent'] = 'CD4 percent cannot be greater than 100'
                }
            }
        },
        confirmatory_hiv_test_type: {
            required: () => true,
            label: 'Confirmatory hiv test type',
            options: () => ([
                { label: 'Rapid antibody test', value: 'HIV rapid test' },
                { label: 'DNA PCR', value: 'HIV DNA polymerase chain reaction' },
                { label: 'Not done', value: 'Not done', disabled: formData.value.has_linkage === 'Yes' }
            ]),
            buildObs: () => {
                return service.buildValueCoded('Confirmatory hiv test type', formData.value.confirmatory_hiv_test_type)
            },
            validation: () => {
                validationMessages.value['confirmatory_hiv_test_type'] = ''
                if (!formData.value.confirmatory_hiv_test_type) {
                    validationMessages.value['confirmatory_hiv_test_type'] = 'Confirmatory hiv test type is required'
                }
            }
        },
        confirmatory_hiv_test_location: {
            required: () => formData.value.confirmatory_hiv_test_type !== 'Not done',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'confirmatory_hiv_test_type' && value === 'Not done') {
                    formData.value.confirmatory_hiv_test_location = ''
                    validationMessages.value['confirmatory_hiv_test_location'] = ''
                }
            },
            label: 'Confirmatory hiv test location',
            buildObs: () => {
                return service.buildValueText('Confirmatory hiv test location', formData.value.confirmatory_hiv_test_location)
            },
            searchFacilities: (q: any) => getFacilities(q).then((res) => facilities.value = res),
            validation: () => {
                validationMessages.value['confirmatory_hiv_test_location'] = ''
                if (!formData.value.confirmatory_hiv_test_location) {
                    validationMessages.value['confirmatory_hiv_test_location'] = 'Confirmatory hiv test location is required'
                }
            }
        },
        confirmatory_hiv_test_date: {
            required: () => formData.value.confirmatory_hiv_test_type !== 'Not done',
            onFormUpdate: (field: string, value: any) => {
                if (field === 'confirmatory_hiv_test_type' && value === 'Not done') {
                    formData.value.confirmatory_hiv_test_date = ''
                    validationMessages.value['confirmatory_hiv_test_date'] = ''
                }
            },
            label: 'Confirmatory hiv test date',
            buildObs: () => {
                return buildDateObs('Confirmatory hiv test date', formData.value.confirmatory_hiv_test_date, false)
            },
            validation: () => {
                validationMessages.value['confirmatory_hiv_test_date'] = ''
                const required = formModel.confirmatory_hiv_test_date.required()
                if (required && !formData.value.confirmatory_hiv_test_date) {
                    validationMessages.value['confirmatory_hiv_test_date'] = 'Confirmatory hiv test date is required'
                    return
                }
                if (required && formData.value.confirmatory_hiv_test_date && new Date(formData.value.confirmatory_hiv_test_date) > new Date()) {
                    validationMessages.value['confirmatory_hiv_test_date'] = 'Confirmatory hiv test date cannot be in the future'
                }
                if (required && formData.value.confirmatory_hiv_test_date && new Date(formData.value.confirmatory_hiv_test_date) < new Date(patient.getBirthdate())) {
                    validationMessages.value['confirmatory_hiv_test_date'] = 'Confirmatory hiv test date cannot be before birthdate'
                }
            }
        }
    }
}

function buildObs() {
    return Object.keys(formModel).reduce((a: any, c: any) => {
        if (c in formModel && formModel[c].required() && typeof formModel[c].buildObs === 'function') {
            const obs = formModel[c].buildObs()
            if (Array.isArray(obs)) {
                return [...a, ...obs]
            }
            return [...a, obs]
        }
        return a
    }, [])
}

function dataHandler(field: string) {
    runValidation(field)
    Object.keys(formModel).forEach((k) => formModel[k]?.onFormUpdate && formModel[k].onFormUpdate(field, formData.value[k]))
}

function buildDateObs(conceptName: string, date: string, isEstimate: boolean) {
    let obs = {}
    if (date.match(/unknown/i)) {
        obs = service.buildValueText(conceptName, 'Unknown')
    } else if (isEstimate) {
        obs = service.buildValueDateEstimated(conceptName, date)
    } else {
        obs = service.buildValueDate(conceptName, date)
    }
    return obs
}

function runValidation(field: string) {
    validationMessages.value[field] = ''
    if (typeof formModel[field].required === 'function') {
        const required = formModel[field].required()
        if (required && field in formData.value && !formData.value[field]) {
            validationMessages.value[field] = 'This field is required'
            return
        }
    }
    if (typeof formModel[field].validation === 'function') {
        formModel[field].validation()
    }
}

function validateAll() {
    Object.keys(formModel).forEach((key: string) => runValidation(key))
    return Object.keys(validationMessages.value).every((key) => `${validationMessages.value[key]}`.length <= 0)
}

async function onSubmit() {
    if (!validateAll()) {
        toastWarning("Please review form for errors")
        return false
    }

    isLoading.value = true
    try {
        await service.createEncounter()
        const obs = await Promise.all(buildObs())
        await service.onSubmit(obs)
        toastSuccess("ART Clinic Registration saved successfully")
        return true
    } catch (e) {
        console.error(e)
        toastDanger("Error has occured while saving observations")
        return false
    } finally {
        isLoading.value = false
    }
}

defineExpose({
    onSubmit
})
</script>

<style scoped>
.error-note {
    margin: 16px;
    padding: 12px;
    border-radius: 8px;
    font-weight: 500;
}

ion-card {
    margin: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

ion-card-header {
    padding-bottom: 8px;
}

ion-card-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--ion-color-primary);
}

ion-item {
    --padding-start: 0;
    --inner-padding-end: 0;
}

ion-label {
    font-weight: 500;
}

ion-note {
    margin-top: 8px;
    margin-left: 16px;
    font-size: 0.875rem;
}

ion-button {
    margin: 16px 0;
    height: 48px;
    font-weight: 600;
}

ion-searchbar {
    --background: var(--ion-color-light);
    --border-radius: 8px;
}

ion-select,
ion-input,
ion-datetime {
    --background: var(--ion-color-light);
    --border-radius: 8px;
}

.conditional-section {
    margin-top: 16px;
    padding: 12px;
    background: var(--ion-color-light-tint);
    border-radius: 8px;
}
</style>